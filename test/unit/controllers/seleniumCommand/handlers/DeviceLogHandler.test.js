/* eslint-disable */

'use strict';

const { DeviceLogHandler } = require('../../../../../controllers/seleniumCommand/handlers/DeviceLogHandler');
const bridge = require('../../../../../bridge');
const requestlib = require('../../../../../lib/request');
const constants = require('../../../../../constants');
const sinon = require('sinon');

describe('DeviceLogHandler tests', () => {
  let sessionKeyObj, request, response, handler;

  beforeEach(() => {
    sessionKeyObj = {
      rails_session_id: 'test-session-id',
      device: 'test-device',
      name: 'test-terminal',
      rproxyHost: 'test-rproxy-host',
      idle_timeout: 300,
      deviceLogs: true,
      deviceLogEndPos: 0,
      debugSession: false
    };
    request = {};
    response = {};
    handler = new DeviceLogHandler(sessionKeyObj, request, response);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('validateRequestData', () => {
    it('should return null for valid logcat request', () => {
      const data = { type: 'logcat' };
      const result = DeviceLogHandler.validateRequestData(data);
      expect(result).toBeNull();
    });

    it('should return null for valid syslog request', () => {
      const data = { type: 'syslog' };
      const result = DeviceLogHandler.validateRequestData(data);
      expect(result).toBeNull();
    });

    it('should return error for missing type', () => {
      const data = {};
      const result = DeviceLogHandler.validateRequestData(data);
      expect(result).toEqual(['Request data must contain a \'type\' key.']);
    });

    it('should return error for invalid log type', () => {
      const data = { type: 'invalid' };
      const result = DeviceLogHandler.validateRequestData(data);
      expect(result).toEqual(['Log type must be either \'logcat\' or \'syslog\'.']);
    });
  });

  describe('processCommand', () => {
    let sendResponseStub, requestlibCallStub;

    beforeEach(() => {
      sendResponseStub = sinon.stub(bridge, 'sendResponse');
      requestlibCallStub = sinon.stub(requestlib, 'call');
    });

    it('should return error when device logs are not enabled', async () => {
      sessionKeyObj.deviceLogs = false;
      const requestStateObj = {};
      const data = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(requestStateObj, data);

      sinon.assert.calledOnce(sendResponseStub);
      const callArgs = sendResponseStub.getCall(0).args;
      expect(callArgs[1].data).toContain('Device logs must be enabled for this session');
    });

    it('should successfully process logcat request', async () => {
      const requestStateObj = {};
      const data = JSON.stringify({ type: 'logcat' });
      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: {
            start_pos: 1234654,
            end_pos: 1239009
          },
          value: [
            {
              timestamp: 1748599327715,
              level: 'ALL',
              message: '--------- beginning of main'
            }
          ]
        })
      };

      requestlibCallStub.resolves(platformResponse);

      await handler.processCommand(requestStateObj, data);

      sinon.assert.calledOnce(requestlibCallStub);
      sinon.assert.calledOnce(sendResponseStub);
      
      const callArgs = sendResponseStub.getCall(0).args;
      const responseData = JSON.parse(callArgs[1].data);
      expect(responseData.status).toBe(0);
      expect(responseData.value).toHaveLength(1);
      expect(responseData.value[0].message).toBe('--------- beginning of main');
      expect(sessionKeyObj.deviceLogEndPos).toBe(1239009);
    });

    it('should successfully process syslog request', async () => {
      const requestStateObj = {};
      const data = JSON.stringify({ type: 'syslog' });
      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: {
            start_pos: 1234654,
            end_pos: 1239009
          },
          value: [
            {
              timestamp: 1748599327715,
              level: 'INFO',
              message: 'System log entry'
            }
          ]
        })
      };

      requestlibCallStub.resolves(platformResponse);

      await handler.processCommand(requestStateObj, data);

      sinon.assert.calledOnce(requestlibCallStub);
      sinon.assert.calledOnce(sendResponseStub);
      
      const callArgs = sendResponseStub.getCall(0).args;
      const responseData = JSON.parse(callArgs[1].data);
      expect(responseData.status).toBe(0);
      expect(responseData.value).toHaveLength(1);
      expect(responseData.value[0].message).toBe('System log entry');
    });

    it('should handle platform error response', async () => {
      const requestStateObj = {};
      const data = JSON.stringify({ type: 'logcat' });
      const platformResponse = {
        statusCode: 500,
        data: 'Internal Server Error'
      };

      requestlibCallStub.resolves(platformResponse);

      await handler.processCommand(requestStateObj, data);

      sinon.assert.calledOnce(sendResponseStub);
      const callArgs = sendResponseStub.getCall(0).args;
      const responseData = JSON.parse(callArgs[1].data);
      expect(responseData.status).toBe(13);
      expect(responseData.value.message).toContain('BROWSERSTACK_INTERNAL_ERROR');
    });

    it('should handle invalid JSON in request data', async () => {
      const requestStateObj = {};
      const data = 'invalid json';

      await handler.processCommand(requestStateObj, data);

      sinon.assert.calledOnce(sendResponseStub);
      const callArgs = sendResponseStub.getCall(0).args;
      const responseData = JSON.parse(callArgs[1].data);
      expect(responseData.status).toBe(13);
      expect(responseData.value.message).toContain('BROWSERSTACK_INTERNAL_ERROR');
    });

    it('should use pagination with stored end position', async () => {
      sessionKeyObj.deviceLogEndPos = 5000;
      const requestStateObj = {};
      const data = JSON.stringify({ type: 'logcat' });
      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 5000, end_pos: 6000 },
          value: []
        })
      };

      requestlibCallStub.resolves(platformResponse);

      await handler.processCommand(requestStateObj, data);

      sinon.assert.calledOnce(requestlibCallStub);
      const callArgs = requestlibCallStub.getCall(0).args;
      expect(callArgs[0].path).toContain('start_pos=5000');
    });
  });
});
